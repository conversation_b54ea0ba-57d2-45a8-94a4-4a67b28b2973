<template>
  <div class="trusteeship-validity">
    <nav-bar @clickLeft="back"></nav-bar>
    <template v-if="isInit">
      <div class="device-configuration" v-for="(item, index) in validityList" :key="'config' + index">
        <div class="configuration-item" @click="chooseItem(item)">
          <div :class="['item-label', activeItem === item.value ? 'active-item' : '']">{{ $t(item.label) }}</div>
          <img
            class="item-img"
            :class="uiStyleFlag === 'ui1b' ? 'vms-item-img' : ''"
            :src="require('@/assets/img/' + appStyleFlag + '/' + uiStyleFlag + '/checked.png')"
            v-if="activeItem === item.value"
          />
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import NavBar from '@/components/NavBar'
import { appClose } from '@/utils/appbridge'
import { TRUSTEESHIP_VALIDITY_LIST_FULLNAME } from '@/utils/options.js'
import { deviceTrusteeshipsUpdate } from '@/api/trusteeship.js'
import { mapState, mapMutations } from 'vuex'
export default {
  name: 'Validity',
  components: {
    NavBar
  },
  props: {},
  data() {
    return {
      validityList: TRUSTEESHIP_VALIDITY_LIST_FULLNAME(),
      activeItem: null, //默认选择永久
      index: null,
      isInit: false,
      from: null,
      eidtId: null
    }
  },
  mounted() {
    this.index = this.$route.params.index
    this.from = this.$route.params.from
    this.eidtId = this.$route.params.id || ''
    this.activeItem = this.availableList[this.index].effectiveTime
      ? parseInt(this.availableList[this.index].effectiveTime)
      : 0
    this.$nextTick(() => {
      setTimeout(() => {
        this.isInit = true
      }, 100)
    })
  },
  computed: {
    ...mapState('app', ['version', 'style', 'language', 'appType']),
    ...mapState('trusteeship', ['availableList']),
    uiStyleFlag() {
      return this.style ? this.style.toLowerCase() : 'ui1a'
    },
    appStyleFlag() {
      return this.appType ? this.appType.toLowerCase() : 'toc'
    }
  },
  methods: {
    ...mapMutations('trusteeship', ['MODIFY_AVAILABLE_LIST']),
    back() {
      this.$router.go(-1)
    },
    chooseItem(item) {
      this.activeItem = item.value
      if (this.from === 'add') {
        // 将列表信息更新到vuex
        let newItem = { ...this.availableList[this.index], effectiveTime: this.activeItem }
        this.MODIFY_AVAILABLE_LIST({
          deleteIndex: this.index,
          data: newItem
        })
        if (this.isInit) this.back()
      } else if (this.from === 'edit') {
        // 从详情过来的编辑 直接调接口保存
        let params = {
          id: this.eidtId,
          effectiveTime: this.activeItem
        }
        this.$loading.show()
        deviceTrusteeshipsUpdate(params)
          .then(() => {
            this.$loading.hide()
            this.$toast(this.$t('operationSuccess'))
            this.back()
          })
          .catch(error => {
            this.$loading.hide()
            if (!error.basic) return
            if (error.basic.code === 32019 || error.basic.code === 32021) {
              this.back()
            } else if (error.basic.code === 32018) {
              setTimeout(() => {
                appClose()
              }, 1000)
            }
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.trusteeship-validity {
  .device-configuration {
    background-color: var(--bg-color-white, #ffffff);
    height: 50px;
    line-height: 50px;
    font-size: var(--font-size-body1-size, 16px);
    font-weight: 500;
    border-bottom: 1px solid var(--brand-bg-color-light-disabled, #f2f4f8);
    .configuration-item {
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .active-item {
        color: var(--text-color-brand, #00baff);
      }
      .item-img {
        width: 15px;
        height: 15px;
      }
      .vms-item-img {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
