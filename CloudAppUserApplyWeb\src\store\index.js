import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import trusteeship from './modules/trusteeship'
import share from './modules/share'
import maxTrusteeship from './modules/maxTrusteeship'
import defense from './modules/defense'
import maxDefense from './modules/maxDefense'
import presetPoint from './modules/presetPoint'
import cruiseLine from './modules/cruiseLine'
import householdManagement from './modules/householdManagement'
import targetFace from './modules/targetFace'
import maxHosting from './modules/maxHosting'
import device from './modules/device'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    trusteeship,
    share,
    maxTrusteeship,
    defense,
    presetPoint,
    cruiseLine,
    householdManagement,
    targetFace,
    maxDefense,
    maxHosting,
    device
  },
  getters
})

export default store
